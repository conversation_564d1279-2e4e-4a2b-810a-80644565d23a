<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>24点检验器</title>
    <style>
        body {
            font-family: 'Comic Sans MS', cursive, sans-serif;
            text-align: center;
            background: linear-gradient(to bottom, #FFD700, #FF69B4);
            color: #333;
            margin: 0;
            padding: 20px;
        }
        h1 {
            font-size: 3em;
            color: #FF4500;
            text-shadow: 2px 2px #87CEEB;
            margin-bottom: 20px;
        }
        .inputs {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px;
        }
        input {
            font-size: 1.5em;
            width: 80px;
            padding: 10px;
            border: 3px solid #00BFFF;
            border-radius: 15px;
            background: #FFFACD;
            text-align: center;
        }
        button {
            font-size: 1.5em;
            margin: 20px;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(to right, #32CD32, #FFD700);
            color: white;
            cursor: pointer;
            box-shadow: 3px 3px 8px rgba(0,0,0,0.3);
            transition: transform 0.2s;
        }
        button:hover {
            transform: scale(1.1);
        }
        #output {
            font-size: 1.8em;
            margin: 20px;
            padding: 20px;
            border-radius: 15px;
            background: #FFF;
            border: 3px solid #FF6347;
            box-shadow: 5px 5px 10px rgba(0,0,0,0.3);
            min-height: 50px;
            color: black;
        }
        .success {
            background: #32CD32;
            color: white;
        }
        .fail {
            background: #FF6347;
            color: white;
        }
    </style>
</head>
<body>
    <h1>24点检验器</h1>
    <p>输入4个数字（1-13），检查是否能得出24点。</p>
    <div class="inputs">
        <input type="number" id="num1" min="1" max="13" placeholder="数字1">
        <input type="number" id="num2" min="1" max="13" placeholder="数字2">
        <input type="number" id="num3" min="1" max="13" placeholder="数字3">
        <input type="number" id="num4" min="1" max="13" placeholder="数字4">
    </div>
    <button onclick="check24()">检查</button>
    <div id="output"></div>

    <script>
        function solve(nums, target) {
            if (nums.length === 1) return nums[0] === target ? [nums[0]] : null;
            for (let i = 0; i < nums.length; i++) {
                for (let j = i + 1; j < nums.length; j++) {
                    let a = nums[i], b = nums[j];
                    let rest = nums.filter((_, idx) => idx !== i && idx !== j);
                    for (let op of ['+', '-', '*', '/']) {
                        let res;
                        if (op === '+') res = a + b;
                        else if (op === '-') res = a - b;
                        else if (op === '*') res = a * b;
                        else if (op === '/' && b !== 0 && a % b === 0) res = a / b;
                        if (res !== undefined && Number.isInteger(res)) {
                            let sub = solve([res, ...rest], target);
                            if (sub) return [a, op, b, ...sub];
                        }
                    }
                }
            }
            return null;
        }

        function formatExpression(arr) {
            let expr = '';
            for (let i = 0; i < arr.length - 1; i += 3) {
                if (i > 0) expr += ', ';
                expr += arr[i] + ' ' + arr[i+1] + ' ' + arr[i+2] + ' = ' + arr[i+3];
            }
            return expr;
        }

        function check24() {
            let nums = [
                parseInt(document.getElementById('num1').value),
                parseInt(document.getElementById('num2').value),
                parseInt(document.getElementById('num3').value),
                parseInt(document.getElementById('num4').value)
            ];
            if (nums.some(isNaN)) {
                document.getElementById('output').innerText = '请输入有效的数字！';
                document.getElementById('output').className = 'fail';
                return;
            }
            let result = solve(nums, 24);
            let outputDiv = document.getElementById('output');
            if (result) {
                outputDiv.innerText = '可以得出24！算式：' + formatExpression(result);
                outputDiv.className = 'success';
            } else {
                outputDiv.innerText = '这些数字无法得出24点。';
                outputDiv.className = 'fail';
            }
            console.log('output text:', outputDiv.innerText);
        }
    </script>
</body>
</html>