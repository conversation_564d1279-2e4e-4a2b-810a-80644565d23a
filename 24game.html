<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>24点游戏</title>
    <style>
        body {
            font-family: 'Comic Sans MS', cursive, sans-serif;
            text-align: center;
            background: linear-gradient(to bottom, #87CEEB, #98FB98);
            color: #333;
            margin: 0;
            padding: 20px;
        }
        h1 {
            font-size: 3em;
            color: #FF4500;
            text-shadow: 2px 2px #FFD700;
            margin-bottom: 20px;
        }
        h2 {
            color: #8A2BE2;
        }
        .cards {
            font-size: 2.5em;
            margin: 30px;
            display: flex;
            justify-content: center;
            gap: 20px;
        }
        .card {
            background: #FFF;
            border: 3px solid #FF69B4;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 5px 5px 10px rgba(0,0,0,0.3);
            display: inline-block;
            min-width: 60px;
            text-align: center;
        }
        input {
            font-size: 1.5em;
            width: 400px;
            padding: 10px;
            border: 3px solid #00BFFF;
            border-radius: 20px;
            margin: 20px;
            background: #FFFACD;
        }
        button {
            font-size: 1.5em;
            margin: 15px;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(to right, #FF6347, #FFD700);
            color: white;
            cursor: pointer;
            box-shadow: 3px 3px 8px rgba(0,0,0,0.3);
            transition: transform 0.2s;
        }
        button:hover {
            transform: scale(1.1);
        }
        #result {
            font-size: 1.8em;
            margin: 20px;
            padding: 10px;
            border-radius: 10px;
        }
        .correct {
            background: #32CD32;
            color: white;
        }
        .incorrect {
            background: #FF6347;
            color: white;
        }
    </style>
</head>
<body>
    <h1>24点游戏</h1>
    <div id="rules">
        <h2>游戏规则</h2>
        <p>随机生成4张扑克牌，使用数字进行加减乘除运算，写出算式得出24。</p>
        <p>人头牌：J=11, Q=12, K=13, A=1</p>
        <p>必须使用所有4张牌，每张牌只能用一次。</p>
    </div>
    <div class="cards" id="cards"></div>
    <input type="text" id="expression" placeholder="输入算式，例如：(3+4)*2+10">
    <br>
    <button onclick="checkAnswer()">检查答案</button>
    <button onclick="newGame()">新游戏</button>
    <div id="result"></div>

    <script>
        let currentCards = [];

        function generateCards() {
            currentCards = [];
            for (let i = 0; i < 4; i++) {
                let num = Math.floor(Math.random() * 13) + 1;
                currentCards.push(num);
            }
            displayCards();
        }

        function displayCards() {
            let cardsDiv = document.getElementById('cards');
            cardsDiv.innerHTML = '';
            currentCards.forEach(num => {
                let cardDiv = document.createElement('div');
                cardDiv.className = 'card';
                let cardName = num === 1 ? 'A' : num === 11 ? 'J' : num === 12 ? 'Q' : num === 13 ? 'K' : num.toString();
                cardDiv.innerText = cardName;
                cardsDiv.appendChild(cardDiv);
            });
        }

        function checkAnswer() {
            let expr = document.getElementById('expression').value;
            let resultDiv = document.getElementById('result');
            resultDiv.className = '';
            try {
                let result = eval(expr);
                if (result === 24) {
                    // 检查是否使用了所有数字
                    let usedNumbers = [];
                    let tempExpr = expr.replace(/[^0-9]/g, ' ').split(' ').filter(n => n !== '').map(n => parseInt(n));
                    usedNumbers = tempExpr;
                    let sortedUsed = usedNumbers.sort((a,b)=>a-b);
                    let sortedCards = currentCards.slice().sort((a,b)=>a-b);
                    if (JSON.stringify(sortedUsed) === JSON.stringify(sortedCards)) {
                        resultDiv.innerText = '🎉 正确！ 🎉';
                        resultDiv.className = 'correct';
                    } else {
                        resultDiv.innerText = '算式正确，但没有使用所有牌或使用了重复牌。';
                        resultDiv.className = 'incorrect';
                    }
                } else {
                    resultDiv.innerText = '算式结果不是24。';
                    resultDiv.className = 'incorrect';
                }
            } catch (e) {
                resultDiv.innerText = '算式无效。';
                resultDiv.className = 'incorrect';
            }
        }

        function newGame() {
            generateCards();
            document.getElementById('expression').value = '';
            let resultDiv = document.getElementById('result');
            resultDiv.innerText = '';
            resultDiv.className = '';
        }

        // 初始化游戏
        newGame();
    </script>
</body>
</html>