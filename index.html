<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>崔菲菲 - 永恒的爱与回忆</title>
    <!-- 引入 Google Fonts - 衬线字体 Playfair Display 和 无衬线字体 Open Sans -->
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <style>
        /* 全局样式 */
        body {
            font-family: 'Open Sans', sans-serif;
            color: #4A4A4A; /* 深灰色文字 */
            background-color: #F8F8F8; /* 柔和的浅米色背景 */
            margin: 0;
            padding: 0;
            line-height: 1.7; /* 增加行高 */
        }

        a {
            text-decoration: none;
            color: #4682B4; /* 沉稳的蓝色链接 */
        }

        a:hover {
            color: #36648B; /* 链接悬停效果 */
        }

        /* 容器 */
        .container {
            max-width: 1000px; /* 网站最大宽度 */
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 页眉 */
        .header {
            text-align: center;
            padding: 30px 0;
            background-color: #FFFFFF; /* 白色背景 */
            box-shadow: 0 2px 5px rgba(0,0,0,0.05); /* 轻微阴影 */
        }

        .header .site-title {
            font-family: 'Playfair Display', serif;
            font-size: 2.5em;
            color: #333333;
            margin: 0;
        }

        .header .nav-menu {
            margin-top: 15px;
        }

        .header .nav-menu a {
            margin: 0 15px;
            font-weight: 600;
            color: #666666;
            transition: color 0.3s ease;
        }

        .header .nav-menu a:hover {
            color: #4682B4;
        }

        /* 主视觉区域 (Hero Section) */
        .hero-section {
            position: relative;
            height: 500px; /* 高度 */
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: #FFFFFF; /* 白色文字 */
            text-align: center;
            background-image: url('https://via.placeholder.com/1500x500/87CEEB/FFFFFF?text=Your+Loved+One\'s+Photo'); /* 替换为你的爱人照片URL */
            background-size: cover;
            background-position: center;
            background-attachment: fixed; /* 视差滚动效果 */
            margin-bottom: 80px; /* 与下方区块的间距 */
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4); /* 半透明黑色叠加层 */
            backdrop-filter: blur(2px); /* 轻微模糊效果 */
        }

        .hero-section .hero-content {
            position: relative;
            z-index: 1;
            padding: 20px;
        }

        .hero-section h1 {
            font-family: 'Playfair Display', serif;
            font-size: 4em;
            margin: 0 0 10px 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5); /* 文字阴影 */
        }

        .hero-section .subtitle {
            font-size: 1.2em;
            font-weight: 400;
            margin-top: 0;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        /* 内容区块 */
        .content-block {
            background-color: #FFFFFF; /* 白色背景 */
            padding: 60px 40px;
            margin-bottom: 60px; /* 区块间距 */
            border-radius: 8px; /* 轻微圆角 */
            box-shadow: 0 4px 15px rgba(0,0,0,0.08); /* 柔和阴影 */
            text-align: center;
        }

        .content-block:nth-child(even) { /* 偶数区块可以有不同的背景色，增加视觉变化 */
            background-color: #FDFDFD; /* 略微不同的浅色 */
        }

        .content-block .icon-wrapper {
            margin-bottom: 25px;
        }

        .content-block .icon-wrapper img {
            width: 80px; /* 图标大小 */
            height: 80px;
            border-radius: 50%; /* 圆形图片 */
            object-fit: cover;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .content-block h2 {
            font-family: 'Playfair Display', serif;
            font-size: 2.2em;
            color: #333333;
            margin-top: 0;
            margin-bottom: 20px;
        }

        .content-block p {
            font-size: 1.1em;
            margin-bottom: 30px;
            max-width: 700px; /* 限制段落宽度，提高可读性 */
            margin-left: auto;
            margin-right: auto;
        }

        .content-block .button {
            display: inline-block;
            width: 200px; /* 统一按钮宽度 */
            padding: 15px 25px;
            background-color: #4682B4; /* 沉稳的蓝色按钮 */
            color: #FFFFFF;
            border-radius: 6px; /* 按钮圆角 */
            font-weight: 600;
            font-size: 1.05em;
            transition: background-color 0.3s ease, transform 0.2s ease;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2); /* 按钮阴影 */
        }

        .content-block .button:hover {
            background-color: #36648B; /* 按钮悬停效果 */
            transform: translateY(-2px); /* 轻微上浮效果 */
        }

        /* 页脚 */
        .footer {
            text-align: center;
            padding: 40px 0;
            background-color: #333333; /* 深色页脚 */
            color: #CCCCCC;
            font-size: 0.9em;
            margin-top: 80px;
        }

        .footer .heart-icon {
            color: #E74C3C; /* 红色心形 */
            margin: 0 5px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .header .site-title {
                font-size: 2em;
            }
            .hero-section {
                height: 400px;
            }
            .hero-section h1 {
                font-size: 3em;
            }
            .hero-section .subtitle {
                font-size: 1em;
            }
            .content-block {
                padding: 40px 20px;
                margin-bottom: 40px;
            }
            .content-block h2 {
                font-size: 1.8em;
            }
            .content-block p {
                font-size: 1em;
            }
            .content-block .button {
                width: 100%; /* 移动端按钮全宽 */
                max-width: 250px; /* 但限制最大宽度 */
            }
        }

        @media (max-width: 480px) {
            .header .nav-menu a {
                display: block;
                margin: 10px 0;
            }
            .hero-section {
                height: 300px;
            }
            .hero-section h1 {
                font-size: 2.5em;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <h1 class="site-title">崔菲菲</h1>
            <nav class="nav-menu">
                <a href="#">首页</a>
                <a href="#">关于</a>
                <a href="#">相册</a>
                <a href="#">留言</a>
            </nav>
        </div>
    </header>

    <section class="hero-section">
        <div class="hero-content">
            <h1>崔菲菲</h1>
            <p class="subtitle">永恒的爱与回忆 | 19XX - 20XX</p>
        </div>
    </section>

    <main class="container">
        <section class="content-block">
            <div class="icon-wrapper">
                <img src="https://via.placeholder.com/80/FFD700/FFFFFF?text=❤️" alt="回忆图标"> <!-- 替换为你的图标或照片 -->
            </div>
            <h2>那些美好的日子</h2>
            <p>她的笑容如同阳光，温暖着我生命中的每一个角落，那些共同度过的时光，永远珍藏在心底。</p>
            <a href="#" class="button">查看回忆</a>
        </section>

        <section class="content-block">
            <div class="icon-wrapper">
                <img src="https://via.placeholder.com/80/A9A9A9/FFFFFF?text=📸" alt="相册图标"> <!-- 替换为你的图标或照片 -->
            </div>
            <h2>珍贵的瞬间</h2>
            <p>每一张照片都承载着一段故事，记录着我们相爱的点点滴滴，是她留给我最宝贵的财富。</p>
            <a href="#" class="button">浏览相册</a>
        </section>

        <section class="content-block">
            <div class="icon-wrapper">
                <img src="https://via.placeholder.com/80/87CEEB/FFFFFF?text=✉️" alt="留言图标"> <!-- 替换为你的图标或照片 -->
            </div>
            <h2>留下你的思念</h2>
            <p>如果你也曾被她的善良和美好所感动，请在这里留下你的心声，让爱与思念延续。</p>
            <a href="#" class="button">写下留言</a>
        </section>
        <iframe src="https://huluwood.cn/wp-admin/admin-ajax.php?action=h5p_embed&id=2" width="787" height="663" frameborder="0" allowfullscreen="allowfullscreen" title="一个h5p地图小游戏"></iframe><script src="https://huluwood.cn/wp-content/plugins/h5p/h5p-php-library/js/h5p-resizer.js" charset="UTF-8"></script>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 崔菲菲纪念网站. 永远在心中 <span class="heart-icon">❤️</span></p>
        </div>
    </footer>
</body>
</html>